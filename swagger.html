<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>واجهة برمجة التطبيقات (API)</title>
    <link rel="stylesheet" href="https://unpkg.com/swagger-ui-dist@4.15.5/swagger-ui.css" />
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 0;
            background-color: #f5f5f5;
        }
        .swagger-ui .topbar {
            background-color: #2c3e50;
        }
        .swagger-ui .info .title {
            color: #2c3e50;
        }
        .custom-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            text-align: center;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .custom-header h1 {
            margin: 0;
            font-size: 2em;
            font-weight: 300;
        }
        .custom-header p {
            margin: 10px 0 0 0;
            opacity: 0.9;
        }
        .auth-section {
            background: white;
            padding: 20px;
            margin: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .auth-form {
            display: flex;
            gap: 10px;
            align-items: center;
            flex-wrap: wrap;
        }
        .auth-form input {
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }
        .auth-form button {
            padding: 8px 16px;
            background: #667eea;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
        }
        .auth-form button:hover {
            background: #5a6fd8;
        }
        .auth-status {
            margin-top: 10px;
            padding: 8px;
            border-radius: 4px;
            font-size: 14px;
        }
        .auth-success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .auth-error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
    </style>
</head>
<body>
    <div class="custom-header">
        <h1>واجهة برمجة التطبيقات (API)</h1>
        <p>API Documentation with JWT Authentication - OAS 3.0</p>
    </div>

    <div class="auth-section">
        <h3>🔐 تسجيل الدخول للحصول على JWT Token</h3>
        <div class="auth-form">
            <input type="text" id="username" placeholder="اسم المستخدم" value="admin">
            <input type="password" id="password" placeholder="كلمة المرور" value="password123">
            <button onclick="login()">تسجيل الدخول</button>
            <button onclick="logout()">تسجيل الخروج</button>
        </div>
        <div id="auth-status"></div>
    </div>

    <div id="swagger-ui"></div>

    <script src="https://unpkg.com/swagger-ui-dist@4.15.5/swagger-ui-bundle.js"></script>
    <script src="https://unpkg.com/swagger-ui-dist@4.15.5/swagger-ui-standalone-preset.js"></script>
    <script>
        let currentToken = localStorage.getItem('jwt_token');
        let swaggerUI;

        // Login function
        async function login() {
            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;
            const statusDiv = document.getElementById('auth-status');

            if (!username || !password) {
                showStatus('يرجى إدخال اسم المستخدم وكلمة المرور', 'error');
                return;
            }

            try {
                const response = await fetch('auth.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ username, password })
                });

                const data = await response.json();

                if (data.success) {
                    currentToken = data.token;
                    localStorage.setItem('jwt_token', currentToken);
                    showStatus(`تم تسجيل الدخول بنجاح! مرحباً ${data.user.username}`, 'success');
                    initSwagger();
                } else {
                    showStatus(data.error || 'فشل في تسجيل الدخول', 'error');
                }
            } catch (error) {
                showStatus('خطأ في الاتصال بالخادم', 'error');
                console.error('Login error:', error);
            }
        }

        // Logout function
        function logout() {
            currentToken = null;
            localStorage.removeItem('jwt_token');
            showStatus('تم تسجيل الخروج', 'success');
            initSwagger();
        }

        // Show status message
        function showStatus(message, type) {
            const statusDiv = document.getElementById('auth-status');
            statusDiv.innerHTML = message;
            statusDiv.className = 'auth-status auth-' + type;
        }

        // Initialize Swagger UI
        function initSwagger() {
            const requestInterceptor = (request) => {
                if (currentToken) {
                    request.headers['X-Authorization'] = 'Bearer ' + currentToken;
                }
                return request;
            };

            swaggerUI = SwaggerUIBundle({
                url: window.location.origin + window.location.pathname.replace('swagger.html', 'api.php/openapi'),
                dom_id: '#swagger-ui',
                deepLinking: true,
                presets: [
                    SwaggerUIBundle.presets.apis,
                    SwaggerUIStandalonePreset
                ],
                plugins: [
                    SwaggerUIBundle.plugins.DownloadUrl
                ],
                layout: "StandaloneLayout",
                tryItOutEnabled: true,
                requestInterceptor: requestInterceptor,
                responseInterceptor: function(response) {
                    if (response.status === 401) {
                        showStatus('انتهت صلاحية الرمز المميز، يرجى تسجيل الدخول مرة أخرى', 'error');
                        logout();
                    }
                    return response;
                }
            });
        }

        // Initialize on page load
        window.onload = function() {
            if (currentToken) {
                showStatus('تم العثور على رمز مميز محفوظ', 'success');
            } else {
                showStatus('يرجى تسجيل الدخول للوصول إلى API', 'error');
            }
            initSwagger();
        };

        // Handle Enter key in password field
        document.addEventListener('DOMContentLoaded', function() {
            document.getElementById('password').addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    login();
                }
            });
        });
    </script>
</body>
</html>
