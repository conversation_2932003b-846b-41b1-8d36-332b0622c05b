-- Database setup for CRUD API with JWT Authentication
-- Create database
CREATE DATABASE IF NOT EXISTS crudd CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE crudd;

-- Users table for authentication
CREATE TABLE IF NOT EXISTS users (
    id INT AUTO_INCREMENT PRIMARY KEY,
    username VA<PERSON>HA<PERSON>(50) UNIQUE NOT NULL,
    password VARCHAR(255) NOT NULL,
    email VARCHAR(100),
    role ENUM('admin', 'user') DEFAULT 'user',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Sample data table
CREATE TABLE IF NOT EXISTS items (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    price DECIMAL(10,2),
    category VARCHAR(50),
    status ENUM('active', 'inactive') DEFAULT 'active',
    user_id INT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL
);

-- Categories table
CREATE TABLE IF NOT EXISTS categories (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(50) NOT NULL,
    description TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Insert sample users (password is 'password123' hashed)
INSERT INTO users (username, password, email, role) VALUES 
('admin', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', '<EMAIL>', 'admin'),
('user1', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', '<EMAIL>', 'user'),
('testuser', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', '<EMAIL>', 'user');

INSERT INTO categories (name, description) VALUES 
('Electronics', 'Electronic devices and gadgets'),
('Books', 'Books and publications'),
('Clothing', 'Apparel and accessories'),
('Home & Garden', 'Home and garden items');

INSERT INTO items (name, description, price, category, status, user_id) VALUES 
('Laptop', 'High-performance laptop', 999.99, 'Electronics', 'active', 1),
('Smartphone', 'Latest smartphone model', 699.99, 'Electronics', 'active', 1),
('Programming Book', 'Learn programming fundamentals', 49.99, 'Books', 'active', 2),
('T-Shirt', 'Cotton t-shirt', 19.99, 'Clothing', 'active', 2),
('Garden Tools', 'Set of garden tools', 89.99, 'Home & Garden', 'active', 3);

-- Create indexes for better performance
CREATE INDEX idx_items_user_id ON items(user_id);
CREATE INDEX idx_items_category ON items(category);
CREATE INDEX idx_items_status ON items(status);
CREATE INDEX idx_users_username ON users(username);
