# واجهة برمجة التطبيقات (API) مع JWT Authentication

## نظرة عامة
هذا المشروع يوفر واجهة برمجة تطبيقات REST API محمية بـ JWT (JSON Web Tokens) باستخدام PHP-CRUD-API.

## الميزات
- 🔐 مصادقة JWT آمنة
- 📊 واجهة Swagger UI تفاعلية
- 🗄️ عمليات CRUD كاملة
- 🌐 دعم CORS
- 📱 واجهة مستخدم عربية

## التثبيت والإعداد

### 1. إعداد قاعدة البيانات
```sql
-- تشغيل ملف setup_database.sql في MySQL
mysql -u root -p < setup_database.sql
```

### 2. تكوين قاعدة البيانات
قم بتحديث إعدادات قاعدة البيانات في `api.php`:
```php
$config = [
    'driver' => 'mysql',
    'address' => 'localhost',
    'port' => 3306,
    'username' => 'root',
    'password' => '',
    'database' => 'crudd',
    // ...
];
```

### 3. تغيير المفتاح السري
⚠️ **مهم**: قم بتغيير المفتاح السري في الملفات التالية:
- `api.php` - السطر 27: `'jwtAuth.secrets'`
- `auth.php` - السطر 19: `private static $secret`

## الاستخدام

### 1. تسجيل الدخول
```bash
curl -X POST http://localhost/crudd/auth.php \
  -H "Content-Type: application/json" \
  -d '{"username":"admin","password":"password123"}'
```

### 2. استخدام API مع JWT
```bash
curl -X GET http://localhost/crudd/api.php/records/items \
  -H "X-Authorization: Bearer YOUR_JWT_TOKEN"
```

### 3. واجهة Swagger
افتح `swagger.html` في المتصفح للحصول على واجهة تفاعلية.

## بيانات الاختبار

### المستخدمون الافتراضيون:
- **admin** / password123 (مدير)
- **user1** / password123 (مستخدم)
- **testuser** / password123 (مستخدم)

### الجداول المتاحة:
- `users` - المستخدمون
- `items` - العناصر
- `categories` - الفئات

## نقاط النهاية (Endpoints)

### المصادقة
- `POST /auth.php` - تسجيل الدخول والحصول على JWT

### البيانات (تتطلب JWT)
- `GET /api.php/records/{table}` - قائمة السзаписей
- `POST /api.php/records/{table}` - إنشاء سجل جديد
- `GET /api.php/records/{table}/{id}` - عرض سجل محدد
- `PUT /api.php/records/{table}/{id}` - تحديث سجل
- `DELETE /api.php/records/{table}/{id}` - حذف سجل

### التوثيق
- `GET /api.php/openapi` - مواصفات OpenAPI
- `GET /swagger.html` - واجهة Swagger UI

## الأمان

### إعدادات JWT
- **انتهاء الصلاحية**: 30 دقيقة
- **الخوارزمية**: HS256
- **الرأس**: X-Authorization
- **التنسيق**: Bearer {token}

### أفضل الممارسات
1. غيّر المفتاح السري في الإنتاج
2. استخدم HTTPS في الإنتاج
3. قم بتشفير كلمات المرور
4. راقب محاولات تسجيل الدخول

## استكشاف الأخطاء

### خطأ 401 Unauthorized
- تأكد من وجود JWT token صحيح
- تحقق من انتهاء صلاحية الرمز المميز
- تأكد من استخدام الرأس الصحيح: `X-Authorization: Bearer {token}`

### خطأ 500 Internal Server Error
- تحقق من إعدادات قاعدة البيانات
- تأكد من وجود الجداول المطلوبة
- راجع سجلات الخادم

### مشاكل CORS
- تأكد من تضمين `cors` في middlewares
- تحقق من إعدادات الخادم

## التطوير

### إضافة middleware جديد
```php
$config['middlewares'] = 'cors,jwtAuth,yourMiddleware';
```

### إضافة جداول جديدة
1. أنشئ الجدول في قاعدة البيانات
2. سيتم إنشاء endpoints تلقائياً
3. حدّث التوثيق إذا لزم الأمر

## الدعم
للمساعدة والدعم، يرجى مراجعة:
- [PHP-CRUD-API Documentation](https://github.com/mevdschee/php-crud-api)
- [JWT.io](https://jwt.io/) لفهم JWT tokens
